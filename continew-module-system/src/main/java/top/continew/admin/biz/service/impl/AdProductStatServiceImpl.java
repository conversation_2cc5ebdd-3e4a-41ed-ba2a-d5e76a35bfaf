package top.continew.admin.biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import top.continew.admin.biz.enums.FbAdOptimizationGoalEnum;
import top.continew.admin.biz.mapper.AdProductStatMapper;
import top.continew.admin.biz.model.entity.AdProductStatDO;
import top.continew.admin.biz.model.entity.CustomerDO;
import top.continew.admin.biz.model.query.AdProductStatQuery;
import top.continew.admin.biz.model.query.CampaignDataQuery;
import top.continew.admin.biz.model.req.AdProductStatReq;
import top.continew.admin.biz.model.resp.AdProductStatDetailResp;
import top.continew.admin.biz.model.resp.AdProductStatResp;
import top.continew.admin.biz.model.resp.CampaignInsightDataResp;
import top.continew.admin.biz.model.resp.CampaignPerformanceResp;
import top.continew.admin.biz.service.AdProductStatService;
import top.continew.admin.biz.service.CustomerService;
import top.continew.starter.core.validation.CheckUtils;
import top.continew.starter.extension.crud.model.query.PageQuery;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.model.resp.PageResp;
import top.continew.starter.extension.crud.service.BaseServiceImpl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 产品日报业务实现
 *
 * <AUTHOR>
 * @since 2025/08/12 15:07
 */
@Service
@RequiredArgsConstructor
public class AdProductStatServiceImpl extends BaseServiceImpl<AdProductStatMapper, AdProductStatDO, AdProductStatResp, AdProductStatDetailResp, AdProductStatQuery, AdProductStatReq> implements AdProductStatService {

    private final CustomerService customerService;

    @Override
    protected void beforeAdd(AdProductStatReq req) {
        CustomerDO customer = customerService.getById(req.getCustomerId());
        AdProductStatDO exist = this.getOne(Wrappers.<AdProductStatDO>lambdaQuery()
                .eq(AdProductStatDO::getProductId, req.getProductId())
                .eq(AdProductStatDO::getStatDate, req.getStatDate())
                .eq(AdProductStatDO::getPlatformAdId, req.getPlatformAdId()));
        CheckUtils.throwIfNotNull(exist, "请勿重复填写");
        req.setFeeRate(customer.getFeeRatePercent());
        BigDecimal fee = (req.getSpend().add(req.getReflowSpend())).multiply(customer.getFeeRatePercent()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        req.setFee(fee);
        super.beforeAdd(req);
    }

    @Override
    protected void beforeUpdate(AdProductStatReq req, Long id) {
        CustomerDO customer = customerService.getById(req.getCustomerId());
        req.setStatDate(null);
        req.setFeeRate(null);
        BigDecimal fee = (req.getSpend().add(req.getReflowSpend())).multiply(customer.getFeeRatePercent()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        req.setFee(fee);
    }

    @Override
    public PageResp<AdProductStatResp> page(AdProductStatQuery query, PageQuery pageQuery) {
        QueryWrapper<AdProductStatDO> queryWrapper = this.buildQueryWrapper(query);
        super.sort(queryWrapper, pageQuery);
        IPage<AdProductStatResp> page = baseMapper.selectCustomPage(new Page<>(pageQuery.getPage(), pageQuery.getSize()), queryWrapper);
        page.getRecords().forEach(this::fill);
        return PageResp.build(page);
    }

    @Override
    protected <E> List<E> list(AdProductStatQuery query, SortQuery sortQuery, Class<E> targetClass) {
        QueryWrapper<AdProductStatDO> queryWrapper = this.buildQueryWrapper(query);
        sort(queryWrapper, sortQuery);
        List<AdProductStatDetailResp> entityList = this.baseMapper.selectCustomList(queryWrapper);
        entityList.forEach(this::fill);
        return BeanUtil.copyToList(entityList, targetClass);
    }

    @Override
    public List<CampaignPerformanceResp> campaignData(CampaignDataQuery query) {

        List<CampaignPerformanceResp> resp = baseMapper.selectCampaignData(query.getAdAccountId(), query.getCustomerId());

        for (int i = 0; i < resp.size(); i++) {
            List<CampaignInsightDataResp> list = baseMapper.selectCampaignInsight(resp.get(i).getPlatformId(), query.getStatDate());

            list.forEach(item -> {
                if (StringUtils.isNotBlank(item.getConversionKey())) {
                    FbAdOptimizationGoalEnum fbEnum = FbAdOptimizationGoalEnum.findByActionType(item.getConversionKey());
                    if (fbEnum == null) {
                        String key = item.getConversionKey().contains(".") ? StrUtil.subAfter(item.getConversionKey(), ".", true) : item.getConversionKey();
                        item.setName(key);
                    } else {
                        item.setName(fbEnum.getDescription());
                    }
                }
            });
        }

        return List.of();
    }
}