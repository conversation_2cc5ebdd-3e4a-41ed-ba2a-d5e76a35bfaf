package top.continew.admin.biz.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import top.continew.admin.biz.model.query.OperatorTaskRecordQuery;
import top.continew.admin.biz.model.resp.crm.OperatorTaskSummaryStatResp;
import top.continew.starter.data.mp.base.BaseMapper;
import top.continew.admin.biz.model.entity.OperatorTaskRecordDO;

import java.util.List;

/**
* 运营人员工作记录 Mapper
*
* <AUTHOR>
* @since 2025/07/21 16:46
*/
public interface OperatorTaskRecordMapper extends BaseMapper<OperatorTaskRecordDO> {
    List<OperatorTaskSummaryStatResp> summaryStat(@Param(Constants.WRAPPER) QueryWrapper<OperatorTaskRecordDO> query);
}