package top.continew.admin.biz.service;

import jakarta.servlet.http.HttpServletResponse;
import top.continew.admin.biz.model.resp.crm.OperatorTaskSummaryStatResp;
import top.continew.starter.extension.crud.model.query.SortQuery;
import top.continew.starter.extension.crud.service.BaseService;
import top.continew.admin.biz.model.query.OperatorTaskRecordQuery;
import top.continew.admin.biz.model.req.OperatorTaskRecordReq;
import top.continew.admin.biz.model.resp.OperatorTaskRecordDetailResp;
import top.continew.admin.biz.model.resp.OperatorTaskRecordResp;

import java.util.List;

/**
 * 运营人员工作记录业务接口
 *
 * <AUTHOR>
 * @since 2025/07/21 16:46
 */
public interface OperatorTaskRecordService extends BaseService<OperatorTaskRecordResp, OperatorTaskRecordDetailResp, OperatorTaskRecordQuery, OperatorTaskRecordReq> {
    List<OperatorTaskSummaryStatResp> summaryStat(OperatorTaskRecordQuery query);

    @Override
    void export(OperatorTaskRecordQuery query, SortQuery sortQuery, HttpServletResponse response);
}