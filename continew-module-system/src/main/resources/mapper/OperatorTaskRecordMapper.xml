<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="top.continew.admin.biz.mapper.OperatorTaskRecordMapper">
    <select id="summaryStat" resultType="top.continew.admin.biz.model.resp.crm.OperatorTaskSummaryStatResp">
        select sum(num) as count, type
        from biz_operator_task_record botr
            ${ew.customSqlSegment}
        group by type
    </select>
</mapper>